package com.airdoc.mpd.gaze

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.utils.GTUtils
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.gaze.camera.GTCameraManager
import com.airdoc.mpd.gaze.camera.ICameraListener
import com.airdoc.mpd.gaze.listener.IGazeTrackListener
import com.airdoc.mpd.gaze.bean.GazeTrackResult
import com.airdoc.mpd.gaze.bean.PostureCalibrationResult
import com.airdoc.mpd.gaze.bean.CalibrationResult
import com.airdoc.mpd.gaze.bean.CalibrateCoordinate
import com.airdoc.mpd.gaze.enumeration.ServiceMode
import com.airdoc.mpd.gaze.track.TrackingManager
import com.airdoc.mpd.gaze.track.GazeWebSocketService
import com.airdoc.mpd.utils.NetworkUtils
import androidx.camera.core.ImageProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.net.InetSocketAddress
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: GazeTrackManager
 * Author by lilin,Date on 2024/7/23 15:49
 * PS: Not easy to write code, please indicate.
 */
object GazeTrackingManager : ICameraListener, IGazeTrackListener {

    private val TAG = GazeTrackingManager::class.java.simpleName

    //Log前缀
    const val PREFIX = "GazeTracking"

    //公共协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // 眼动追踪是否已启动
    private val isGazeTrackingStarted = AtomicBoolean(false)

    // WebSocket服务器实例 - 已移动到GazeTrackService中管理
     private var webSocketService: GazeWebSocketService? = null

    // 外部监听器
    private var externalListener: IGazeTrackListener? = null

    /**
     * 设置眼动追踪监听器
     */
    fun setGazeTrackListener(listener: IGazeTrackListener?) {
        externalListener = listener
    }

    /**
     * 启动眼动追踪（包含摄像头和补光灯）
     * @param context 上下文
     * @param lifecycleOwner 生命周期管理者
     * @return true 启动成功，false 启动失败
     */
    suspend fun startGazeTracking(context: Context, lifecycleOwner: LifecycleOwner): Boolean {
        Logger.d(TAG, msg = "startGazeTracking isStarted = ${isGazeTrackingStarted.get()}")

        if (isGazeTrackingStarted.get()) {
            Logger.w(TAG, msg = "Gaze tracking is already started")
            return true
        }

        return try {
            Logger.d(TAG, msg = "🚀 开始启动眼动追踪流程...")

            // 步骤0: 拷贝模型和配置文件
            Logger.d(TAG, msg = "📋 步骤0: 拷贝模型和配置文件")
            copyModel2Dir(context)
            Logger.i(TAG, msg = "✅ 模型和配置文件拷贝完成")

            // 步骤1: WebSocket服务器已在GazeTrackService中启动
            Logger.d(TAG, msg = "📋 步骤1: WebSocket服务器已在GazeTrackService中启动，跳过")

            // 检查补光灯控制节点状态
            Logger.d(TAG, msg = "📋 步骤2: 检查补光灯控制节点")
//            GTCameraManager.checkFillLightLampNode()

            // 设置摄像头监听器
            Logger.d(TAG, msg = "📋 步骤3: 设置摄像头监听器")
            GTCameraManager.setCameraListener(this)

            // 启动摄像头（会自动开启补光灯）
            Logger.d(TAG, msg = "📋 步骤4: 启动摄像头和补光灯")
            GTCameraManager.startCamera(context, lifecycleOwner)

            // 设置TrackingManager的生命周期和监听器
            Logger.d(TAG, msg = "📋 步骤5: 设置TrackingManager")
            TrackingManager.setLifecycleOwner(lifecycleOwner)
            TrackingManager.setGazeTrackListener(this)

            // 启动视线追踪
            Logger.d(TAG, msg = "📋 步骤6: 启动视线追踪算法")
            val trackingResult = TrackingManager.startTracking(context)
            Logger.d(TAG, msg = "TrackingManager.startTracking result: $trackingResult")

            isGazeTrackingStarted.set(true)
            Logger.i(TAG, msg = "✅ 眼动追踪启动流程完成")
            true
        } catch (e: Exception) {
            Logger.e(TAG, msg = "❌ 眼动追踪启动失败: ${e.message}")
            Logger.e(TAG, msg = "异常堆栈: ${e.stackTraceToString()}")
            false
        }
    }

    /**
     * 停止眼动追踪
     * @param context 上下文
     */
    fun stopGazeTracking(context: Context) {
        Logger.d(TAG, msg = "stopGazeTracking isStarted = ${isGazeTrackingStarted.get()}")

        if (!isGazeTrackingStarted.get()) {
            Logger.w(TAG, msg = "Gaze tracking is not started")
            return
        }

        try {
            // 停止视线追踪
            val trackingResult = TrackingManager.stopTracking()
            Logger.d(TAG, msg = "TrackingManager.stopTracking result: $trackingResult")

            // 停止摄像头（会自动关闭补光灯）
            GTCameraManager.stopCamera(context)

            // WebSocket服务器由GazeTrackService管理，这里不需要停止
            // stopWebSocketServer()

            // 清除监听器
            GTCameraManager.setCameraListener(null)

            isGazeTrackingStarted.set(false)
            Logger.i(TAG, msg = "Gaze tracking stopped successfully")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "Failed to stop gaze tracking: ${e.message}")
        }
    }

    /**
     * 检查眼动追踪是否已启动
     */
    fun isGazeTrackingStarted(): Boolean {
        return isGazeTrackingStarted.get()
    }

    /**
     * 初始化视线追踪
     */
    fun initGazeTracking(context: Context){
        coroutineScope.launch {
            copyModel2Dir(context)
        }
    }

    /**
     * 获取是否显示视点
     */
    fun getDisplayViewpoint():Boolean{
        return MMKVManager.decodeBool(CommonPreference.DISPLAY_VIEWPOINT)?:false
    }

    /**
     * 设置是否显示视点
     */
    fun setDisplayViewpoint(isShow:Boolean){
        MMKVManager.encodeBool(CommonPreference.DISPLAY_VIEWPOINT,isShow)
    }

    /**
     * 拷贝模型到本地文件
     */
    private suspend fun copyModel2Dir(context: Context){
        val weightDir = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE)
        val assetManager = context.assets
        try {
            // 列出assets文件夹下的所有文件和子文件夹
            val assetsList = assetManager.list(GazeConstants.MODEL_DIR_NAME)
            if (!assetsList.isNullOrEmpty()){
                for (assets in assetsList) {
                    if (assets.endsWith(GazeConstants.MODEL_FILE_EXTENSION)) {
                        withContext(Dispatchers.IO){
                            // 如果文件以 .rknn 结尾，就添加到列表中
                            val weightFile = File(weightDir, assets)
                            GTUtils.copyAssets2Dir(context, weightFile, "${GazeConstants.MODEL_DIR_NAME}/$assets")
                        }
                    }
                }
            }

            // 复制新增的两个文件：face_landmarker.task 和 opencv.js
            withContext(Dispatchers.IO) {
                // 复制 face_landmarker.task 文件
                val faceLandmarkerFile = File(weightDir, "face_landmarker.task")
                val faceLandmarkerSuccess = GTUtils.copyAssets2Dir(context, faceLandmarkerFile, "${GazeConstants.MODEL_DIR_NAME}/face_landmarker.task")
                Logger.d(TAG, msg = "复制face_landmarker.task文件: $faceLandmarkerSuccess, 路径: ${faceLandmarkerFile.absolutePath}")

                // 复制 opencv.js 文件
                val opencvJsFile = File(weightDir, "opencv.js")
                val opencvJsSuccess = GTUtils.copyAssets2Dir(context, opencvJsFile, "${GazeConstants.MODEL_DIR_NAME}/opencv.js")
                Logger.d(TAG, msg = "复制opencv.js文件: $opencvJsSuccess, 路径: ${opencvJsFile.absolutePath}")
            }

            // 复制默认校准参数文件
            withContext(Dispatchers.IO) {
                copyDefaultCalibrationParams(context, weightDir)
            }
        } catch (e: IOException) {
            // 处理异常情况
            e.printStackTrace()
        }
    }

    /**
     * 复制默认校准参数文件
     */
    private fun copyDefaultCalibrationParams(context: Context, configDir: File) {
        try {
            val calibParamFile = File(configDir, "calib_param.txt")
            val success = GTUtils.copyAssets2Dir(context, calibParamFile, "${GazeConstants.MODEL_DIR_NAME}/calib_param.txt")
            Logger.d(TAG, msg = "复制默认校准参数文件: $success, 路径: ${calibParamFile.absolutePath}")

            if (success && calibParamFile.exists()) {
                Logger.i(TAG, msg = "默认校准参数文件复制成功，可以直接开始追踪")
            } else {
                Logger.e(TAG, msg = "默认校准参数文件复制失败")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "复制默认校准参数文件异常: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 启动WebSocket服务器
     */
    private suspend fun startWebSocketServer(context: Context) {
        withContext(Dispatchers.IO) {
            try {
                // 如果WebSocket服务已经启动，先停止
                webSocketService?.let {
                    Logger.d(TAG, msg = "WebSocket服务已存在，先停止旧服务")
                    it.stop()
                    webSocketService = null
                }

                // 动态获取设备IP地址
                val deviceIP = NetworkUtils.getDeviceIPAddress()
                val port = 9200

                Logger.d(TAG, msg = "准备启动WebSocket服务器")
                Logger.d(TAG, msg = "  设备IP: $deviceIP")
                Logger.d(TAG, msg = "  端口: $port")

                val inetSocketAddress = if (deviceIP.isNotEmpty()) {
                    InetSocketAddress(deviceIP as String, port)
                } else {
                    // 如果无法获取IP，绑定到所有接口
                    Logger.w(TAG, msg = "无法获取设备IP，绑定到所有接口")
                    InetSocketAddress(port)
                }

                webSocketService = GazeWebSocketService(inetSocketAddress).apply {
                    isReuseAddr = true
                }
                webSocketService?.start()

                val actualIP = if (deviceIP.isNotEmpty()) deviceIP else "0.0.0.0"
                Logger.i(TAG, msg = "✓ WebSocket服务器启动成功")
                Logger.i(TAG, msg = "  监听IP: $actualIP")
                Logger.i(TAG, msg = "  监听端口: $port")
                Logger.i(TAG, msg = "  Web连接地址: ws://$actualIP:$port")

                // 保存当前WebSocket地址供其他组件使用
                NetworkUtils.setCurrentWebSocketAddress("ws://$actualIP:$port")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "WebSocket服务器启动失败: ${e.message}")
                Logger.e(TAG, msg = "  错误详情: ${e.stackTraceToString()}")
                throw e
            }
        }
    }

    /**
     * 停止WebSocket服务器
     */
    private fun stopWebSocketServer() {
        try {
            webSocketService?.let {
                Logger.d(TAG, msg = "正在停止WebSocket服务器")
                it.stop()
                Logger.i(TAG, msg = "✓ WebSocket服务器已停止")
            }
            webSocketService = null
        } catch (e: Exception) {
            Logger.e(TAG, msg = "停止WebSocket服务器失败: ${e.message}")
        }
    }

    // =============== ICameraListener 实现 ===============

    override fun onCameraStatusChange(isOn: Boolean) {
        Logger.d(TAG, msg = "onCameraStatusChange isOn = $isOn")
        externalListener?.let { listener ->
            coroutineScope.launch {
                // 可以在这里通知外部监听器摄像头状态变化
            }
        }
    }

//    override fun onAnalyze(image: ImageProxy) {
//        // 打印每一帧的图像分辨率信息
//        Logger.i(TAG, prefix = PREFIX, msg = "📸 每帧图像分辨率 - 实际: ${image.width}x${image.height}, 期望: ${GazeConstants.IMAGE_WIDTH}x${GazeConstants.IMAGE_HEIGHT}")
//        Logger.i(TAG, prefix = PREFIX, msg = "📸 图像详细信息 - 格式: ${image.format}, 裁剪区域: ${image.cropRect}, 平面数: ${image.planes.size}")
//
//        // 检查分辨率是否匹配
//        if (image.width != GazeConstants.IMAGE_WIDTH || image.height != GazeConstants.IMAGE_HEIGHT) {
//            Logger.w(TAG, prefix = PREFIX, msg = "⚠️ 分辨率不匹配警告 - 当前: ${image.width}x${image.height}, 需要: ${GazeConstants.IMAGE_WIDTH}x${GazeConstants.IMAGE_HEIGHT}")
//        } else {
//            Logger.d(TAG, prefix = PREFIX, msg = "✅ 分辨率匹配正确")
//        }
//
//        // 调用TrackingManager进行实际的视线追踪
//        TrackingManager.sendImageProxy(image)
//    }

    // =============== IGazeTrackListener 实现 ===============

    override fun onGazeTracking(gazeTrackResult: GazeTrackResult) {
        // 广播眼动数据到WebSocket客户端
        broadcastGazeData(gazeTrackResult)
        
        // 转发给外部监听器
        externalListener?.onGazeTracking(gazeTrackResult)
    }

    /**
     * 广播眼动数据到WebSocket客户端
     */
    private fun broadcastGazeData(gazeTrackResult: GazeTrackResult) {
        try {
            webSocketService?.let { service ->
                // 构建JSON数据
                val jsonData = buildString {
                    append("{")
                    append("\"valid\":${gazeTrackResult.valid},")
                    append("\"x\":${gazeTrackResult.x},")
                    append("\"y\":${gazeTrackResult.y},")
                    append("\"dist\":${gazeTrackResult.dist},")
                    append("\"duration\":${gazeTrackResult.duration},")
                    append("\"skew\":${gazeTrackResult.skew},")
                    append("\"timestamp\":${gazeTrackResult.timestamp}")
                    append("}")
                }
                
                // 广播数据
                service.broadcast(jsonData)
                
                Logger.d(TAG, prefix = PREFIX, msg = "WebSocket广播眼动数据: $jsonData")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "WebSocket广播眼动数据失败: ${e.message}")
        }
    }

}