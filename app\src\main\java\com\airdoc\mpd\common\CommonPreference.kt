package com.airdoc.mpd.common

import com.airdoc.component.common.cache.INameSpace
import com.airdoc.mpd.device.enumeration.StartupMode

/**
 * FileName: CommonPreference
 * Author by lilin,Date on 2025/4/7 10:21
 * PS: Not easy to write code, please indicate.
 */
enum class CommonPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * APP 域名
     */
    APP_DOMAIN(null),

    /**
     * 用户语言 如：简体中文 (zh-CN),美式英语 (en-US)
     */
    USER_LANGUAGE(null),

    /**
     * 本地启动模式[StartupMode]
     */
    LOCAL_START_UP_MODE(null),

    /**
     * 是否显示视点
     */
    DISPLAY_VIEWPOINT(false),

    /**
     * 是否启用主动欢迎
     */
    ENABLE_PROACTIVE_GREETING(true),

    /**
     * 是否启用指尖式数据采集
     */
    ENABLE_FINGERTIP_DATA_COLLECTION(false),

    /**
     * 采集器编号
     */
    COLLECTOR_NUMBER(""),

    /**
     * 模型版本
     */
    MODEL_VERSION("V1.0.0.1"),

    /**
     * 测试版版本
     */
    TEST_VERSION("V2.0.1"),

    /**
     * 设备提醒最后显示日期
     */
    DEVICE_REMINDER_LAST_SHOW_DATE(null);

    override fun getNameSpace(): String {
        return "CommonPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}