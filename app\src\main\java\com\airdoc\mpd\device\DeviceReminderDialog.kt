package com.airdoc.mpd.device

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.R

/**
 * FileName: DeviceReminderDialog
 * Author by lilin,Date on 2025/8/1
 * PS: Not easy to write code, please indicate.
 * 设备信息提醒对话框
 */
class DeviceReminderDialog(
    context: Context,
    private val reminderInfo: DeviceReminderInfo,
    private val onConfirm: (() -> Unit)? = null,
    private val onCancel: (() -> Unit)? = null
) : BaseCommonDialog(context) {

    companion object {
        private const val TAG = "DeviceReminderDialog"
    }

    private val tvOpenDate by id<TextView>(R.id.tv_open_date)
    private val tvValidUntil by id<TextView>(R.id.tv_valid_until)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val tvCancel by id<TextView>(R.id.tv_cancel)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_device_reminder)

        initView()
        initListener()
    }

    private fun initView() {
        Logger.d(TAG, msg = "显示设备提醒: ${reminderInfo.type}, 消息: ${reminderInfo.message}")

        // 根据提醒类型显示对应的消息
        when (reminderInfo.type) {
            DeviceReminderType.EXPIRY -> {
                tvOpenDate.isVisible = true
                tvValidUntil.isVisible = false
                tvOpenDate.text = reminderInfo.message
            }
            DeviceReminderType.USAGE -> {
                tvOpenDate.isVisible = false
                tvValidUntil.isVisible = true
                tvValidUntil.text = reminderInfo.message
            }
        }
    }

    private fun initListener() {
        // 取消按钮
        tvCancel.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击取消按钮")
            DeviceReminderManager.markReminderShownToday()
            dismiss()
            onCancel?.invoke()
        }

        // 确定按钮
        tvOk.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击确定按钮")
            // 标记今天已经显示过提醒
            DeviceReminderManager.markReminderShownToday()
            dismiss()
            onConfirm?.invoke()
        }
    }
}
