package com.airdoc.mpd.detection.hrv

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Color
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.print.PrintAttributes
import android.print.PrintManager
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.airdoc.mpd.R
import com.airdoc.mpd.detection.DetectionActivity
import com.airdoc.mpd.gaze.GazeConstants
import kotlinx.coroutines.launch
import com.airdoc.mpd.gaze.GazeTrackingManager
import com.airdoc.mpd.gaze.track.GazeTrackService

/**
 * FileName: HrvActivity
 * Author by lilin,Date on 2025/4/9 16:34
 * PS: Not easy to write code, please indicate.
 */
class HrvActivity : BaseCommonActivity(), HrvWebView.HrvActionListener {

    companion object{
        internal val TAG = HrvActivity::class.java.simpleName

        private const val INPUT_PARAM_URL = "url"

        fun createIntent(context: Context,url:String): Intent {
            val intent = Intent(context, HrvActivity::class.java)
            intent.putExtra(INPUT_PARAM_URL,url)
            return intent
        }
    }

    private val wbHrv by id<HrvWebView>(R.id.wb_hrv)
    private val llLoading by id<LinearLayout>(R.id.ll_loading)

    private var mHrvUrl = ""

    private val handler = object :  LifecycleHandler(Looper.getMainLooper(),this) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }


    var mServiceManager : Messenger? = null

    private var mClientMessage: Messenger = Messenger(handler)


    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG,msg = "HrvActivity onServiceConnected")
            if (service != null){
                mServiceManager = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceManager?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            mServiceManager = null
        }
    }

    private fun parseMessage(msg: Message) {

    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_hrv)

        // 在Activity创建时清除WebView缓存
        clearWebViewCache()

        initParam()
        initView()

    }

    private fun initParam() {
        mHrvUrl = intent.getStringExtra(INPUT_PARAM_URL) ?: ""
    }

    private fun initView() {
        try {
            // 设置监听器
            wbHrv.setHrvActionListener(this)

            // 添加JavaScript接口
            wbHrv.addJavascriptInterface(wbHrv.HrvAction(), "android")
            Logger.d(TAG, msg = "JavaScript接口已添加: android")

            llLoading.isVisible = true
            wbHrv.setBackgroundColor(Color.TRANSPARENT)

            loadUrl()
        } catch (e: Exception) {
            Logger.e(TAG, msg = "initView error: ${e.message}")
        }
    }

    private fun loadUrl(){
        if (mHrvUrl.isNotEmpty()){
            wbHrv.loadUrl(mHrvUrl)
        }
    }

    /**
     * 清除WebView缓存
     */
    private fun clearWebViewCache() {
        wbHrv.clearWebViewCache()
    }

    override fun onPageFinished() {
        llLoading.isVisible = false
    }

    override fun onFinish() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun goHome() {
        lifecycleScope.launch {
            val intent = Intent()
            intent.putExtra(DetectionActivity.PARAM_IS_GO_HOME,true)
            setResult(RESULT_OK,intent)
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(wbHrv)
        }
    }

    override fun onReady() {

    }

    override fun onStartGazeTracking() {
        Logger.d(TAG, msg = "🚀 HrvActivity - 收到启动眼动追踪请求")
        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "正在调用GazeTrackingManager.startGazeTracking...")
                // 启动眼动追踪
                val success = GazeTrackingManager.startGazeTracking(this@HrvActivity, this@HrvActivity)
                Logger.i(TAG, msg = "眼动追踪启动结果: $success")

                if (success) {
                    // 可以通过JavaScript回调通知Web页面启动成功
                    runOnUiThread {
                        try {
                            wbHrv.evaluateJavascript("javascript:if(typeof onGazeTrackingStarted === 'function') onGazeTrackingStarted(true);") { result ->
                                Logger.d(TAG, msg = "JavaScript回调结果: $result")
                            }
                            Logger.i(TAG, msg = "✓ 眼动追踪启动成功，已通知Web页面")
                        } catch (e: Exception) {
                            Logger.e(TAG, msg = "JavaScript回调异常: ${e.message}")
                        }
                    }
                } else {
                    // 启动失败，通知Web页面
                    runOnUiThread {
                        try {
                            wbHrv.evaluateJavascript("javascript:if(typeof onGazeTrackingStarted === 'function') onGazeTrackingStarted(false);") { result ->
                                Logger.d(TAG, msg = "JavaScript回调结果: $result")
                            }
                            Logger.w(TAG, msg = "✗ 眼动追踪启动失败，已通知Web页面")
                        } catch (e: Exception) {
                            Logger.e(TAG, msg = "JavaScript回调异常: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "启动眼动追踪异常: ${e.message}")
                runOnUiThread {
                    try {
                        wbHrv.evaluateJavascript("javascript:if(typeof onGazeTrackingStarted === 'function') onGazeTrackingStarted(false);") { result ->
                            Logger.d(TAG, msg = "异常情况JavaScript回调结果: $result")
                        }
                    } catch (jsException: Exception) {
                        Logger.e(TAG, msg = "异常情况JavaScript回调失败: ${jsException.message}")
                    }
                }
            }
        }
    }

    override fun onStopGazeTracking() {
        Logger.d(TAG, msg = "🛑 HrvActivity - 收到停止眼动追踪请求")
        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "正在调用GazeTrackingManager.stopGazeTracking...")
                // 停止眼动追踪
                GazeTrackingManager.stopGazeTracking(this@HrvActivity)
                Logger.i(TAG, msg = "✓ 眼动追踪已停止")

                // 通知Web页面停止成功
                runOnUiThread {
                    try {
                        wbHrv.evaluateJavascript("javascript:if(typeof onGazeTrackingStopped === 'function') onGazeTrackingStopped();") { result ->
                            Logger.d(TAG, msg = "停止JavaScript回调结果: $result")
                        }
                        Logger.d(TAG, msg = "已通知Web页面眼动追踪停止")
                    } catch (jsException: Exception) {
                        Logger.e(TAG, msg = "停止JavaScript回调异常: ${jsException.message}")
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "停止眼动追踪异常: ${e.message}")
            }
        }
    }

    override fun onGazeTrackingStatus(isEnabled: Boolean) {
        Logger.d(TAG, msg = "⚙️ HrvActivity - 收到眼动追踪状态变化请求: $isEnabled")
        lifecycleScope.launch {
            try {
                // 处理眼动追踪状态变化
                if (isEnabled) {
                    Logger.d(TAG, msg = "状态设置为启用，调用启动方法")
                    onStartGazeTracking()
                } else {
                    Logger.d(TAG, msg = "状态设置为禁用，调用停止方法")
                    onStopGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "处理眼动追踪状态变化异常: ${e.message}")
            }
        }
    }

    override fun generatePpgAnalysisReport(): String? {
        return null
    }

    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }

    override fun onPause() {
        super.onPause()
        wbHrv.onPause()
        wbHrv.pauseTimers()
        this.unbindService(serviceConnection)
    }

    override fun onResume() {
        super.onResume()
        wbHrv.resumeTimers()
        wbHrv.onResume()
        this.bindService(Intent(this,GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onDestroy() {
        Logger.d(TAG, msg = "📱 HrvActivity onDestroy - 清理资源")

        // 停止GazeTrackService前台服务以释放资源
        try {
            Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
            stopService(Intent(this, GazeTrackService::class.java))
            Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
        }

        wbHrv.destroy()
        super.onDestroy()
        Logger.d(TAG, msg = "📱 HrvActivity onDestroy 完成")
    }

}